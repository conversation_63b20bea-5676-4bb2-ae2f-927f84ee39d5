{"name": "Oleer - Online Study Hub", "description": "A secure marketplace platform for buying and selling digital items with cryptocurrency payments", "version": "1.0.0", "type": "django-web-application", "main_directory": "batch_marketplace", "structure": {"root": ".", "django_project": "batch_marketplace", "main_app": "batch_marketplace/marketplace", "static_files": "batch_marketplace/marketplace/static", "templates": "batch_marketplace/marketplace/templates", "media": "batch_marketplace/media"}, "languages": ["python", "javascript", "html", "css"], "frameworks": ["django", "tailwindcss", "bootstrap"], "database": "sqlite3", "deployment": {"platforms": ["railway", "render", "docker"], "production_ready": true}, "development": {"python_version": "3.11", "virtual_env": "venv", "requirements_file": "requirements.txt", "manage_py": "batch_marketplace/manage.py"}, "entry_points": {"django_settings": "batch.settings", "wsgi": "batch.wsgi:application", "manage": "batch_marketplace/manage.py"}}