<!DOCTYPE html>
{% load static %}
<html lang="en" class="h-full scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="{% block description %}Modern marketplace for VPS and server resources - Buy and sell digital items with cryptocurrency{% endblock %}">
    <meta name="keywords" content="{% block keywords %}marketplace, VPS, servers, cloud computing, cryptocurrency, digital items{% endblock %}">
    <meta name="author" content="Oleer Market">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ request.build_absolute_uri }}">
    <meta property="og:title" content="{% block og_title %}{% block title %}Oleer Market{% endblock %}{% endblock %}">
    <meta property="og:description" content="{% block og_description %}{% block description %}Modern marketplace for VPS and server resources{% endblock %}{% endblock %}">
    <meta property="og:image" content="{% static 'img/og-image.jpg' %}">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="{{ request.build_absolute_uri }}">
    <meta property="twitter:title" content="{% block twitter_title %}{% block title %}Oleer Market{% endblock %}{% endblock %}">
    <meta property="twitter:description" content="{% block twitter_description %}{% block description %}Modern marketplace for VPS and server resources{% endblock %}{% endblock %}">
    <meta property="twitter:image" content="{% static 'img/twitter-image.jpg' %}">

    <title>{% block title %}Oleer Market{% endblock %}</title>

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        // Modern Brand Colors
                        brand: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                            950: '#082f49',
                        },
                        primary: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a',
                            950: '#020617',
                        },
                        success: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                            950: '#052e16',
                        },
                        warning: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            200: '#fde68a',
                            300: '#fcd34d',
                            400: '#fbbf24',
                            500: '#f59e0b',
                            600: '#d97706',
                            700: '#b45309',
                            800: '#92400e',
                            900: '#78350f',
                            950: '#451a03',
                        },
                        danger: {
                            50: '#fef2f2',
                            100: '#fee2e2',
                            200: '#fecaca',
                            300: '#fca5a5',
                            400: '#f87171',
                            500: '#ef4444',
                            600: '#dc2626',
                            700: '#b91c1c',
                            800: '#991b1b',
                            900: '#7f1d1d',
                            950: '#450a0a',
                        },
                        info: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                            950: '#172554',
                        },
                        // Semantic UI Colors
                        surface: {
                            DEFAULT: '#ffffff',
                            secondary: '#f8fafc',
                            tertiary: '#f1f5f9',
                        },
                        border: {
                            DEFAULT: '#e2e8f0',
                            secondary: '#cbd5e1',
                        },
                        text: {
                            primary: '#0f172a',
                            secondary: '#475569',
                            tertiary: '#64748b',
                        },
                        // Legacy colors for compatibility
                        secondary: '#2563eb',
                        accent: '#f43f5e',
                        sidebar: {
                            bg: '#0f172a',
                            color: '#cbd5e1',
                            hover: '#334155',
                            active: '#0ea5e9',
                        },
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
                        mono: ['JetBrains Mono', 'ui-monospace', 'SFMono-Regular', 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', 'monospace'],
                    },
                    width: {
                        'sidebar': '280px',
                        'sidebar-collapsed': '80px',
                    },
                    boxShadow: {
                        'card': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
                        'card-hover': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                        'dropdown': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
                        'glow': '0 0 15px rgba(14, 165, 233, 0.5)',
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.3s ease-out',
                        'fade-out': 'fadeOut 0.3s ease-out',
                        'slide-up': 'slideUp 0.3s ease-out',
                        'slide-down': 'slideDown 0.3s ease-out',
                        'scale-in': 'scaleIn 0.2s ease-out',
                        'scale-out': 'scaleOut 0.2s ease-out',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        fadeOut: {
                            '0%': { opacity: '1' },
                            '100%': { opacity: '0' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                        slideDown: {
                            '0%': { transform: 'translateY(-20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                        scaleIn: {
                            '0%': { transform: 'scale(0.9)', opacity: '0' },
                            '100%': { transform: 'scale(1)', opacity: '1' },
                        },
                        scaleOut: {
                            '0%': { transform: 'scale(1)', opacity: '1' },
                            '100%': { transform: 'scale(0.9)', opacity: '0' },
                        },
                    }
                }
            }
        }
    </script>

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/tailwind.css' %}">
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">

    <!-- Heroicons -->
    <script src="https://unpkg.com/heroicons@2.0.18/24/outline/index.js" type="module"></script>
    <script src="https://unpkg.com/heroicons@2.0.18/24/solid/index.js" type="module"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{% static 'img/favicon.png' %}">
    <link rel="apple-touch-icon" sizes="180x180" href="{% static 'img/apple-touch-icon.png' %}">
    <link rel="icon" type="image/png" sizes="32x32" href="{% static 'img/favicon-32x32.png' %}">
    <link rel="icon" type="image/png" sizes="16x16" href="{% static 'img/favicon-16x16.png' %}">

    <!-- Theme color -->
    <meta name="theme-color" content="#0ea5e9">
    <meta name="msapplication-TileColor" content="#0ea5e9">

    {% block extra_css %}{% endblock %}

    <style>
        /* Custom styles to fix sidebar overlap */
        @media (min-width: 1024px) {
            .lg\:ml-sidebar {
                margin-left: 260px; /* Match the sidebar width */
            }
        }

        /* Ensure content doesn't overlap with sidebar on mobile */
        @media (max-width: 1023px) {
            #sidebar {
                box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
                width: 260px; /* Ensure consistent width on mobile */
            }

            /* Fix for mobile sidebar toggle */
            #sidebar.translate-x-0 {
                transform: translateX(0);
            }

            #sidebar.-translate-x-full {
                transform: translateX(-100%);
            }
        }
    </style>
</head>
<body class="bg-surface font-sans antialiased text-text-primary min-h-full" x-data="{ sidebarOpen: false, darkMode: false }" :class="{ 'dark': darkMode }">
    {% if user.is_authenticated %}
    <!-- Modern Sidebar -->
    <div class="sidebar sidebar-modern"
         :class="sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'"
         id="sidebar">
        <!-- Sidebar Header -->
        <div class="sidebar-header bg-primary-900/50 backdrop-blur-sm">
            <a href="{% url 'index' %}" class="flex items-center space-x-3 text-white font-bold group">
                <div class="w-10 h-10 rounded-xl bg-brand-500 flex items-center justify-center shadow-lg group-hover:shadow-glow transition-all duration-200">
                    <i class="fas fa-store text-lg text-white"></i>
                </div>
                <span class="text-xl font-semibold tracking-wide bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">Oleer Market</span>
            </a>
            <button class="text-primary-300 hover:text-white focus:outline-none focus:text-white lg:hidden transition-colors duration-200"
                    @click="sidebarOpen = false">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <!-- Enhanced User Info -->
        <div class="px-6 py-5 border-b border-primary-700/30 bg-primary-800/30 backdrop-blur-sm">
            <div class="flex items-center space-x-3">
                <div class="relative">
                    <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-brand-400 to-brand-600 flex items-center justify-center shadow-lg">
                        <i class="fas fa-user text-lg text-white"></i>
                    </div>
                    <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-success-500 rounded-full border-2 border-primary-900 flex items-center justify-center">
                        <div class="w-2 h-2 bg-white rounded-full"></div>
                    </div>
                </div>
                <div class="flex-1 min-w-0">
                    <h3 class="text-sm font-semibold text-white truncate">{{ user.username }}</h3>
                    <div class="flex items-center gap-2 mt-1.5">
                        <span class="badge badge-sm
                            {% if user.profile.role == 'buyer' %}bg-info-500/20 text-info-300 border border-info-500/30
                            {% elif user.profile.role == 'seller' %}bg-success-500/20 text-success-300 border border-success-500/30
                            {% else %}bg-secondary-500/20 text-secondary-300 border border-secondary-500/30{% endif %}">
                            <i class="fas fa-{% if user.profile.role == 'buyer' %}shopping-cart{% elif user.profile.role == 'seller' %}store{% else %}user{% endif %} text-xs mr-1"></i>
                            {{ user.profile.role|title }}
                        </span>
                    </div>
                    <div class="flex items-center gap-2 mt-2">
                        {% if user.profile.role == 'buyer' %}
                            <div class="flex items-center px-2.5 py-1 rounded-lg bg-success-500/10 border border-success-500/20">
                                <i class="fas fa-wallet text-xs text-success-400 mr-1.5"></i>
                                <span class="text-xs font-medium text-success-300">${{ user.profile.balance|floatformat:2 }}</span>
                            </div>
                        {% endif %}
                        {% if user.profile.role == 'seller' %}
                            <div class="flex items-center px-2.5 py-1 rounded-lg bg-warning-500/10 border border-warning-500/20">
                                <i class="fas fa-coins text-xs text-warning-400 mr-1.5"></i>
                                <span class="text-xs font-medium text-warning-300">${{ total_gained_balance|default:"0.00"|floatformat:2 }}</span>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Sidebar Navigation -->
        <nav class="sidebar-nav flex-1 px-3 py-6 space-y-1 overflow-y-auto scrollbar-thin">
            <!-- General Section -->
            <div class="px-3 mb-4">
                <h4 class="text-xs font-semibold text-primary-400 uppercase tracking-wider mb-3">General</h4>
                <div class="space-y-1">
                    <a href="{% url 'index' %}" class="sidebar-link group {% if request.path == '/' %}sidebar-link-active{% else %}sidebar-link-inactive{% endif %}">
                        <div class="sidebar-icon bg-gradient-to-br from-brand-400 to-brand-600 rounded-lg p-1.5 {% if request.path == '/' %}shadow-glow{% endif %}">
                            <i class="fas fa-home text-sm text-white"></i>
                        </div>
                        <span class="font-medium">Dashboard</span>
                        {% if request.path == '/' %}
                            <div class="ml-auto w-2 h-2 bg-brand-400 rounded-full animate-pulse"></div>
                        {% endif %}
                    </a>

                    <a href="{% url 'vps_list' %}" class="sidebar-link group {% if 'products' in request.path %}sidebar-link-active{% else %}sidebar-link-inactive{% endif %}">
                        <div class="sidebar-icon bg-gradient-to-br from-info-400 to-info-600 rounded-lg p-1.5 {% if 'products' in request.path %}shadow-glow{% endif %}">
                            <i class="fas fa-list text-sm text-white"></i>
                        </div>
                        <span class="font-medium">Browse Items</span>
                        {% if 'products' in request.path %}
                            <div class="ml-auto w-2 h-2 bg-info-400 rounded-full animate-pulse"></div>
                        {% endif %}
                    </a>

                    <a href="{% url 'ticket_list' %}" class="sidebar-link group {% if 'tickets' in request.path %}sidebar-link-active{% else %}sidebar-link-inactive{% endif %}">
                        <div class="sidebar-icon bg-gradient-to-br from-warning-400 to-warning-600 rounded-lg p-1.5 {% if 'tickets' in request.path %}shadow-glow{% endif %}">
                            <i class="fas fa-ticket-alt text-sm text-white"></i>
                        </div>
                        <span class="font-medium">Support Tickets</span>
                        {% if 'tickets' in request.path %}
                            <div class="ml-auto w-2 h-2 bg-warning-400 rounded-full animate-pulse"></div>
                        {% endif %}
                    </a>

                    {% if user.profile.role == 'support' or user.profile.role == 'admin' %}
                    <a href="{% url 'user_reports' %}" class="sidebar-link group {% if 'reports' in request.path %}sidebar-link-active{% else %}sidebar-link-inactive{% endif %}">
                        <div class="sidebar-icon bg-gradient-to-br from-danger-400 to-danger-600 rounded-lg p-1.5 {% if 'reports' in request.path %}shadow-glow{% endif %}">
                            <i class="fas fa-flag text-sm text-white"></i>
                        </div>
                        <span class="font-medium">Reports</span>
                        {% if 'reports' in request.path %}
                            <div class="ml-auto w-2 h-2 bg-danger-400 rounded-full animate-pulse"></div>
                        {% endif %}
                    </a>
                    {% endif %}
                </div>
            </div>

            <!-- Role-specific Sections -->
            {% if user.profile.role == 'buyer' %}
            <!-- Buyer Tools Section -->
            <div class="px-3 mb-4">
                <h4 class="text-xs font-semibold text-info-400 uppercase tracking-wider mb-3">Buyer Tools</h4>
                <div class="space-y-1">
                    <a href="{% url 'buyer_dashboard' %}" class="sidebar-link group {% if 'buyer' in request.path %}sidebar-link-active{% else %}sidebar-link-inactive{% endif %}">
                        <div class="sidebar-icon bg-gradient-to-br from-info-400 to-info-600 rounded-lg p-1.5 {% if 'buyer' in request.path %}shadow-glow{% endif %}">
                            <i class="fas fa-shopping-cart text-sm text-white"></i>
                        </div>
                        <span class="font-medium">My Purchases</span>
                        {% if 'buyer' in request.path %}
                            <div class="ml-auto w-2 h-2 bg-info-400 rounded-full animate-pulse"></div>
                        {% endif %}
                    </a>

                    <a href="{% url 'user_reports' %}" class="sidebar-link group {% if 'reports' in request.path %}sidebar-link-active{% else %}sidebar-link-inactive{% endif %}">
                        <div class="sidebar-icon bg-gradient-to-br from-warning-400 to-warning-600 rounded-lg p-1.5 {% if 'reports' in request.path %}shadow-glow{% endif %}">
                            <i class="fas fa-flag text-sm text-white"></i>
                        </div>
                        <span class="font-medium">My Reports</span>
                        {% if 'reports' in request.path %}
                            <div class="ml-auto w-2 h-2 bg-warning-400 rounded-full animate-pulse"></div>
                        {% endif %}
                    </a>

                    <a href="{% url 'topup_balance' %}" class="sidebar-link group {% if 'topup-balance' in request.path %}sidebar-link-active{% else %}sidebar-link-inactive{% endif %}">
                        <div class="sidebar-icon bg-gradient-to-br from-success-400 to-success-600 rounded-lg p-1.5 {% if 'topup-balance' in request.path %}shadow-glow{% endif %}">
                            <i class="fas fa-wallet text-sm text-white"></i>
                        </div>
                        <span class="font-medium">Top Up Balance</span>
                        {% if 'topup-balance' in request.path %}
                            <div class="ml-auto w-2 h-2 bg-success-400 rounded-full animate-pulse"></div>
                        {% endif %}
                    </a>
                </div>
            </div>

            {% elif user.profile.role == 'seller' %}
            <!-- Seller Tools Section -->
            <div class="px-3 mb-4">
                <h4 class="text-xs font-semibold text-success-400 uppercase tracking-wider mb-3">Seller Tools</h4>
                <div class="space-y-1">
                    <a href="{% url 'seller_dashboard' %}" class="sidebar-link group {% if 'seller' in request.path %}sidebar-link-active{% else %}sidebar-link-inactive{% endif %}">
                        <div class="sidebar-icon bg-gradient-to-br from-success-400 to-success-600 rounded-lg p-1.5 {% if 'seller' in request.path %}shadow-glow{% endif %}">
                            <i class="fas fa-store text-sm text-white"></i>
                        </div>
                        <span class="font-medium">My Items</span>
                        {% if 'seller' in request.path %}
                            <div class="ml-auto w-2 h-2 bg-success-400 rounded-full animate-pulse"></div>
                        {% endif %}
                    </a>

                    <a href="{% url 'user_reports' %}" class="sidebar-link group {% if 'reports' in request.path %}sidebar-link-active{% else %}sidebar-link-inactive{% endif %}">
                        <div class="sidebar-icon bg-gradient-to-br from-warning-400 to-warning-600 rounded-lg p-1.5 {% if 'reports' in request.path %}shadow-glow{% endif %}">
                            <i class="fas fa-flag text-sm text-white"></i>
                        </div>
                        <span class="font-medium">My Reports</span>
                        {% if 'reports' in request.path %}
                            <div class="ml-auto w-2 h-2 bg-warning-400 rounded-full animate-pulse"></div>
                        {% endif %}
                    </a>

                    <a href="{% url 'add_account' %}" class="sidebar-link group {% if 'add-account' in request.path %}sidebar-link-active{% else %}sidebar-link-inactive{% endif %}">
                        <div class="sidebar-icon bg-gradient-to-br from-brand-400 to-brand-600 rounded-lg p-1.5 {% if 'add-account' in request.path %}shadow-glow{% endif %}">
                            <i class="fas fa-plus-circle text-sm text-white"></i>
                        </div>
                        <span class="font-medium">Add Item</span>
                        {% if 'add-account' in request.path %}
                            <div class="ml-auto w-2 h-2 bg-brand-400 rounded-full animate-pulse"></div>
                        {% endif %}
                    </a>

                    <a href="{% url 'withdrawal_request' %}" class="sidebar-link group {% if 'withdrawals' in request.path %}sidebar-link-active{% else %}sidebar-link-inactive{% endif %}">
                        <div class="sidebar-icon bg-gradient-to-br from-warning-400 to-warning-600 rounded-lg p-1.5 {% if 'withdrawals' in request.path %}shadow-glow{% endif %}">
                            <i class="fas fa-wallet text-sm text-white"></i>
                        </div>
                        <span class="font-medium">Withdraw Funds</span>
                        {% if 'withdrawals' in request.path %}
                            <div class="ml-auto w-2 h-2 bg-warning-400 rounded-full animate-pulse"></div>
                        {% endif %}
                    </a>
                </div>
            </div>

            {% elif user.profile.role == 'admin' %}
            <!-- Administration Section -->
            <div class="px-3 mb-4">
                <h4 class="text-xs font-semibold text-secondary-400 uppercase tracking-wider mb-3">Administration</h4>
                <div class="space-y-1">
                    <a href="{% url 'admin_dashboard' %}" class="sidebar-link group {% if 'admin-dashboard' in request.path %}sidebar-link-active{% else %}sidebar-link-inactive{% endif %}">
                        <div class="sidebar-icon bg-gradient-to-br from-secondary-400 to-secondary-600 rounded-lg p-1.5 {% if 'admin-dashboard' in request.path %}shadow-glow{% endif %}">
                            <i class="fas fa-shield-alt text-sm text-white"></i>
                        </div>
                        <span class="font-medium">Admin Panel</span>
                        {% if 'admin-dashboard' in request.path %}
                            <div class="ml-auto w-2 h-2 bg-secondary-400 rounded-full animate-pulse"></div>
                        {% endif %}
                    </a>
                </div>
            </div>
            {% endif %}

            <!-- Account Section -->
            <div class="px-3 mt-6">
                <h4 class="text-xs font-semibold text-primary-400 uppercase tracking-wider mb-3">Account</h4>
                <div class="space-y-1">
                    <!-- Dark Mode Toggle -->
                    <button @click="darkMode = !darkMode" class="sidebar-link group sidebar-link-inactive w-full text-left">
                        <div class="sidebar-icon bg-gradient-to-br from-neutral-400 to-neutral-600 rounded-lg p-1.5">
                            <i class="fas fa-moon text-sm text-white" x-show="!darkMode"></i>
                            <i class="fas fa-sun text-sm text-white" x-show="darkMode"></i>
                        </div>
                        <span class="font-medium" x-text="darkMode ? 'Light Mode' : 'Dark Mode'">Dark Mode</span>
                    </button>

                    <a href="{% url 'logout' %}" class="sidebar-link group text-danger-400 hover:text-danger-300 hover:bg-danger-500/10">
                        <div class="sidebar-icon bg-gradient-to-br from-danger-400 to-danger-600 rounded-lg p-1.5">
                            <i class="fas fa-sign-out-alt text-sm text-white"></i>
                        </div>
                        <span class="font-medium">Logout</span>
                    </a>
                </div>
            </div>
        </nav>
        </div>
    </div>
    {% endif %}

    <!-- Sidebar Overlay -->
    <div class="fixed inset-0 z-overlay bg-black/50 transition-opacity duration-300 ease-in-out lg:hidden"
         :class="sidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'"
         @click="sidebarOpen = false"></div>

    <!-- Main Content Wrapper -->
    <div class="{% if user.is_authenticated %}lg:ml-sidebar{% endif %} min-h-screen flex flex-col transition-all duration-300 relative">
        <!-- Modern Top Navbar -->
        <header class="navbar-glass sticky top-0 z-navbar backdrop-blur-md border-b border-border/50">
            <div class="container-fluid">
                <div class="flex justify-between items-center h-16">
                    <div class="flex items-center space-x-4">
                        {% if user.is_authenticated %}
                        <!-- Enhanced Mobile menu button -->
                        <button type="button"
                                class="btn btn-ghost btn-sm lg:hidden focus-ring"
                                @click="sidebarOpen = !sidebarOpen">
                            <span class="sr-only">Open sidebar</span>
                            <i class="fas fa-bars text-lg"></i>
                        </button>
                        {% endif %}

                        <!-- Enhanced Logo -->
                        <a href="{% url 'index' %}" class="flex items-center space-x-3 group">
                            <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-brand-500 to-brand-600 flex items-center justify-center shadow-md group-hover:shadow-glow transition-all duration-200">
                                <i class="fas fa-store text-sm text-white"></i>
                            </div>
                            <span class="font-bold text-text-primary text-lg bg-gradient-to-r from-brand-600 to-brand-800 bg-clip-text text-transparent">Oleer Market</span>
                        </a>
                    </div>

                    <!-- Right side navigation -->
                    <div class="flex items-center">
                        {% if not user.is_authenticated %}
                        <!-- Public Navigation -->
                        <div class="hidden md:flex md:items-center md:space-x-2">
                            <a href="{% url 'vps_list' %}" class="nav-link {% if 'products' in request.path %}nav-link-active{% else %}nav-link-inactive{% endif %}">
                                <i class="fas fa-server text-sm"></i>
                                <span>Browse Items</span>
                            </a>
                            <a href="{% url 'login' %}" class="nav-link {% if 'login' in request.path %}nav-link-active{% else %}nav-link-inactive{% endif %}">
                                <i class="fas fa-sign-in-alt text-sm"></i>
                                <span>Login</span>
                            </a>
                            <a href="{% url 'register' %}" class="btn btn-primary btn-sm ml-2">
                                <i class="fas fa-user-plus text-sm"></i>
                                <span>Register</span>
                            </a>
                        </div>

                        <!-- Enhanced Mobile menu dropdown -->
                        <div class="flex md:hidden" x-data="{ mobileMenuOpen: false }">
                            <button type="button" class="btn btn-ghost btn-sm focus-ring"
                                    @click="mobileMenuOpen = !mobileMenuOpen">
                                <span class="sr-only">Open menu</span>
                                <i class="fas fa-bars text-lg"></i>
                            </button>

                            <!-- Enhanced Mobile menu panel -->
                            <div x-show="mobileMenuOpen"
                                 class="dropdown-menu right-0 top-full mt-2 w-48 animate-fade-in"
                                 x-transition:enter="transition ease-out duration-200"
                                 x-transition:enter-start="opacity-0 scale-95"
                                 x-transition:enter-end="opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-150"
                                 x-transition:leave-start="opacity-100 scale-100"
                                 x-transition:leave-end="opacity-0 scale-95"
                                 @click.away="mobileMenuOpen = false">
                                <a href="{% url 'vps_list' %}" class="dropdown-item {% if 'products' in request.path %}bg-surface-secondary{% endif %}">
                                    <i class="fas fa-server text-sm mr-2"></i> Browse Items
                                </a>
                                <div class="dropdown-divider"></div>
                                <a href="{% url 'login' %}" class="dropdown-item {% if 'login' in request.path %}bg-surface-secondary{% endif %}">
                                    <i class="fas fa-sign-in-alt text-sm mr-2"></i> Login
                                </a>
                                <a href="{% url 'register' %}" class="dropdown-item {% if 'register' in request.path %}bg-surface-secondary{% endif %}">
                                    <i class="fas fa-user-plus text-sm mr-2"></i> Register
                                </a>
                            </div>
                        </div>
                        {% else %}
                        <!-- Notifications dropdown -->
                        <div class="ml-4 relative flex-shrink-0" x-data="{ open: false }" x-dropdown>
                            <button type="button" id="mainNotificationButton" class="p-1 rounded-full text-gray-500 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary relative notification-button">
                                <span class="sr-only">View notifications</span>
                                <i class="fas fa-bell text-xl"></i>
                                {% if unread_notification_count > 0 %}
                                <span class="absolute top-0 right-0 block h-5 w-5 rounded-full bg-red-500 text-white text-xs font-medium flex items-center justify-center transform -translate-y-1/4 translate-x-1/4">
                                    {{ unread_notification_count }}
                                </span>
                                {% endif %}
                            </button>

                            <!-- Notifications panel -->
                            <div id="mainNotificationMenu"
                                 class="origin-top-right absolute right-0 mt-2 w-80 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none hidden dropdown-menu notification-dropdown"
                                 x-transition:enter="transition ease-out duration-200"
                                 x-transition:enter-start="opacity-0 scale-95"
                                 x-transition:enter-end="opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-150"
                                 x-transition:leave-start="opacity-100 scale-100"
                                 x-transition:leave-end="opacity-0 scale-95">
                                <div class="py-1">
                                    <div class="px-4 py-2 border-b border-gray-200">
                                        <p class="text-sm font-medium text-gray-700">
                                            {% if unread_notification_count > 0 %}
                                            <i class="fas fa-bell mr-2 text-secondary"></i>{{ unread_notification_count }} New Notifications
                                            {% else %}
                                            <i class="fas fa-bell mr-2"></i>No New Notifications
                                            {% endif %}
                                        </p>
                                    </div>

                                    <div class="max-h-60 overflow-y-auto">
                                        {% for notification in recent_notifications|slice:":3" %}
                                        <div class="px-4 py-3 hover:bg-gray-50 relative group">
                                            <a href="{{ notification.link|default:'#' }}" class="block">
                                                <div class="flex">
                                                    <div class="flex-shrink-0 mr-3">
                                                        <div class="h-8 w-8 rounded-full bg-blue-100 text-blue-500 flex items-center justify-center">
                                                            <i class="fas fa-bell"></i>
                                                        </div>
                                                    </div>
                                                    <div class="flex-1 min-w-0">
                                                        <p class="text-sm font-medium text-gray-900 truncate">{{ notification.title }}</p>
                                                        <p class="text-sm text-gray-500 truncate">{{ notification.message|truncatewords:10 }}</p>
                                                        <p class="text-xs text-gray-400 mt-1">{{ notification.created_at|timesince }} ago</p>
                                                    </div>
                                                </div>
                                            </a>
                                            <button type="button"
                                                    class="absolute top-3 right-4 text-gray-400 hover:text-gray-600 notification-delete"
                                                    data-notification-id="{{ notification.id }}">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                        {% empty %}
                                        <div class="px-4 py-6 text-center text-sm text-gray-500">
                                            You have no new notifications.
                                        </div>
                                        {% endfor %}
                                    </div>

                                    <div class="border-t border-gray-200 py-2 px-4">
                                        <a href="{% url 'notifications' %}" class="block text-center text-sm font-medium text-blue-600 hover:text-blue-800">
                                            View All Notifications
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Profile dropdown -->
                        <div class="ml-4 relative flex-shrink-0">
                            <button type="button" id="mainProfileButton" class="flex items-center space-x-2 text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary profile-button">
                                <span class="sr-only">Open user menu</span>
                                <i class="fas fa-user-circle text-2xl text-gray-500"></i>
                                <span class="hidden md:block font-medium text-gray-700">{{ user.username }}</span>
                                <i class="fas fa-chevron-down text-xs text-gray-500"></i>
                            </button>

                            <!-- Profile dropdown panel -->
                            <div id="mainProfileMenu"
                                 class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none hidden dropdown-menu profile-dropdown"
                                 x-transition:enter="transition ease-out duration-200"
                                 x-transition:enter-start="opacity-0 scale-95"
                                 x-transition:enter-end="opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-150"
                                 x-transition:leave-start="opacity-100 scale-100"
                                 x-transition:leave-end="opacity-0 scale-95">
                                <div class="py-1">
                                    <a href="{% url 'profile' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-user mr-2"></i> My Profile
                                    </a>
                                    <div class="border-t border-gray-100 my-1"></div>
                                    <a href="{% url 'logout' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-sign-out-alt mr-2"></i> Logout
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </nav>

        <!-- Messages -->
        {% if messages %}
        <div class="px-4 sm:px-6 lg:px-8 py-4">
            {% for message in messages %}
            <div class="rounded-md p-4 mb-4 {% if message.tags == 'success' %}bg-green-50 text-green-800{% elif message.tags == 'info' %}bg-blue-50 text-blue-800{% elif message.tags == 'warning' %}bg-yellow-50 text-yellow-800{% elif message.tags == 'error' %}bg-red-50 text-red-800{% else %}bg-gray-50 text-gray-800{% endif %} shadow-sm relative"
                 x-data="{ show: true }"
                 x-show="show"
                 x-transition:leave="transition ease-in duration-300"
                 x-transition:leave-start="opacity-100"
                 x-transition:leave-end="opacity-0">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas {% if message.tags == 'success' %}fa-check-circle text-green-500{% elif message.tags == 'info' %}fa-info-circle text-blue-500{% elif message.tags == 'warning' %}fa-exclamation-triangle text-yellow-500{% elif message.tags == 'error' %}fa-times-circle text-red-500{% else %}fa-bell text-gray-500{% endif %}"></i>
                    </div>
                    <div class="ml-3 flex-1">
                        <p class="text-sm">{{ message }}</p>
                    </div>
                    <div class="ml-auto pl-3">
                        <div class="-mx-1.5 -my-1.5">
                            <button type="button"
                                    class="inline-flex rounded-md p-1.5 {% if message.tags == 'success' %}text-green-500 hover:bg-green-100{% elif message.tags == 'info' %}text-blue-500 hover:bg-blue-100{% elif message.tags == 'warning' %}text-yellow-500 hover:bg-yellow-100{% elif message.tags == 'error' %}text-red-500 hover:bg-red-100{% else %}text-gray-500 hover:bg-gray-100{% endif %} focus:outline-none"
                                    @click="show = false">
                                <span class="sr-only">Dismiss</span>
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- Main Content -->
        <main class="flex-grow px-4 sm:px-6 lg:px-8 py-6">
            {% block content %}{% endblock %}
        </main>

        <!-- Footer -->
        <footer class="bg-white border-t border-gray-200 py-6">
            <div class="px-4 sm:px-6 lg:px-8">
                <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                    <div>
                        <p class="text-sm text-gray-500">
                            <a href="{% url 'index' %}" class="font-semibold text-gray-700 hover:text-blue-500">Oleer Market</a> &copy; 2025
                        </p>
                    </div>
                    <div>
                        <ul class="flex space-x-6">
                            <li>
                                <a href="#" class="text-sm text-gray-500 hover:text-gray-700">Support</a>
                            </li>
                            <li>
                                <a href="#" class="text-sm text-gray-500 hover:text-gray-700">Help Center</a>
                            </li>
                            <li>
                                <a href="#" class="text-sm text-gray-500 hover:text-gray-700">Privacy</a>
                            </li>
                            <li>
                                <a href="#" class="text-sm text-gray-500 hover:text-gray-700">Terms</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- Back to top button -->
    <button type="button"
            class="fixed bottom-6 right-6 p-3 bg-secondary hover:bg-blue-600 text-white rounded-full shadow-lg transition-all duration-300 opacity-0 pointer-events-none transform translate-y-10"
            id="btn-back-to-top">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.12.3/dist/cdn.min.js"></script>

    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>

    <!-- Ensure Alpine.js is loaded -->
    <script>
        // Vanilla JS fallback function for toggling sidebar
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebarOverlay');

            if (sidebar) {
                if (sidebar.classList.contains('-translate-x-full')) {
                    sidebar.classList.remove('-translate-x-full');
                    sidebar.classList.add('translate-x-0');
                    if (sidebarOverlay) {
                        sidebarOverlay.classList.remove('hidden', 'opacity-0', 'pointer-events-none');
                        sidebarOverlay.classList.add('opacity-100', 'pointer-events-auto');
                    }
                } else {
                    sidebar.classList.add('-translate-x-full');
                    sidebar.classList.remove('translate-x-0');
                    if (sidebarOverlay) {
                        sidebarOverlay.classList.add('opacity-0', 'pointer-events-none');
                        sidebarOverlay.classList.remove('opacity-100', 'pointer-events-auto');
                    }
                }
            }
        }

        // Check if Alpine.js is loaded after a short delay
        setTimeout(function() {
            if (typeof Alpine === 'undefined') {
                console.error('Alpine.js failed to load. Using fallback.');
                // Add fallback for sidebar toggle
                const sidebarToggleBtn = document.getElementById('sidebarToggle');

                // We don't need to add event listener here since we added onclick handler directly
                // But we'll keep this as a backup
                if (sidebarToggleBtn) {
                    sidebarToggleBtn.addEventListener('click', toggleSidebar);
                }
            }
        }, 1000);
    </script>

    <!-- Additional scripts -->
    <script>
        // Initialize Alpine.js store for sidebar state
        document.addEventListener('alpine:init', () => {
            // Global variable to track open Alpine dropdowns
            window.openAlpineDropdown = null;

            // Ensure all dropdowns are hidden by default and manage interactions
            Alpine.directive('dropdown', (el) => {
                el._x_dropdown = {
                    init() {
                        // Add hidden class to dropdown elements
                        const dropdown = el.querySelector('[x-show="open"]');
                        if (dropdown && !dropdown.classList.contains('hidden')) {
                            dropdown.classList.add('hidden');
                        }

                        // Add click handler to toggle button
                        const button = el.querySelector('[x-data]');
                        if (button) {
                            button.addEventListener('click', () => {
                                // Close any vanilla JS dropdowns when Alpine dropdown is opened
                                if (window.currentOpenDropdown) {
                                    window.currentOpenDropdown.classList.add('hidden');
                                    window.currentOpenDropdown = null;
                                }

                                // Close any other Alpine dropdowns
                                if (window.openAlpineDropdown && window.openAlpineDropdown !== el) {
                                    const openDropdown = window.openAlpineDropdown.querySelector('[x-show="open"]');
                                    if (openDropdown && !openDropdown.classList.contains('hidden')) {
                                        window.openAlpineDropdown.__x.setData('open', false);
                                    }
                                }

                                // Set this as the currently open Alpine dropdown
                                window.openAlpineDropdown = el;

                                // Close sidebar on mobile when opening dropdown
                                if (window.innerWidth < 1024) {
                                    const sidebar = document.getElementById('sidebar');
                                    const sidebarOverlay = document.getElementById('sidebarOverlay');
                                    if (sidebar && !sidebar.classList.contains('-translate-x-full')) {
                                        sidebar.classList.add('-translate-x-full');
                                        sidebar.classList.remove('translate-x-0');
                                        if (sidebarOverlay) {
                                            sidebarOverlay.classList.add('opacity-0', 'pointer-events-none');
                                            sidebarOverlay.classList.remove('opacity-100', 'pointer-events-auto');
                                        }
                                        Alpine.store('sidebar').open = false;
                                    }
                                }
                            });
                        }
                    }
                };
                el._x_dropdown.init();
            });

            Alpine.store('sidebar', {
                open: false,
                toggle() {
                    this.open = !this.open;

                    // Close any open dropdowns when toggling sidebar
                    if (this.open) {
                        // Close any vanilla JS dropdowns
                        if (window.currentOpenDropdown) {
                            window.currentOpenDropdown.classList.add('hidden');
                            window.currentOpenDropdown = null;
                        }

                        // Close any Alpine.js dropdowns
                        if (window.openAlpineDropdown) {
                            const openDropdown = window.openAlpineDropdown.querySelector('[x-show="open"]');
                            if (openDropdown && !openDropdown.classList.contains('hidden')) {
                                window.openAlpineDropdown.__x.setData('open', false);
                            }
                            window.openAlpineDropdown = null;
                        }

                        // Close all other dropdowns
                        document.querySelectorAll('.dropdown-menu').forEach(dropdown => {
                            dropdown.classList.add('hidden');
                        });
                    }

                    // Update sidebar and overlay classes
                    const sidebar = document.getElementById('sidebar');
                    const sidebarOverlay = document.getElementById('sidebarOverlay');

                    if (sidebar && sidebarOverlay) {
                        if (this.open) {
                            sidebar.classList.remove('-translate-x-full');
                            sidebar.classList.add('translate-x-0');
                            sidebarOverlay.classList.remove('opacity-0', 'pointer-events-none');
                            sidebarOverlay.classList.add('opacity-100', 'pointer-events-auto');
                        } else {
                            sidebar.classList.add('-translate-x-full');
                            sidebar.classList.remove('translate-x-0');
                            sidebarOverlay.classList.add('opacity-0', 'pointer-events-none');
                            sidebarOverlay.classList.remove('opacity-100', 'pointer-events-auto');
                        }
                    }
                }
            });
        });

        document.addEventListener('DOMContentLoaded', function() {
            // Back to top button functionality
            const backToTopButton = document.getElementById('btn-back-to-top');

            if (backToTopButton) {
                window.addEventListener('scroll', function() {
                    if (window.pageYOffset > 300) {
                        backToTopButton.classList.remove('opacity-0', 'pointer-events-none', 'translate-y-10');
                        backToTopButton.classList.add('opacity-100', 'pointer-events-auto', 'translate-y-0');
                    } else {
                        backToTopButton.classList.add('opacity-0', 'pointer-events-none', 'translate-y-10');
                        backToTopButton.classList.remove('opacity-100', 'pointer-events-auto', 'translate-y-0');
                    }
                });

                backToTopButton.addEventListener('click', function() {
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });
                });
            }

            // Function to handle dropdown toggles
            function setupDropdown(buttonId, menuId) {
                const button = document.getElementById(buttonId);
                const menu = document.getElementById(menuId);

                if (!button || !menu) return;

                // Remove Alpine.js attributes from parent div to avoid conflicts
                const parentDiv = button.closest('[x-data]');
                if (parentDiv) {
                    parentDiv.removeAttribute('x-data');
                    parentDiv.removeAttribute('x-dropdown');
                }

                // Add click event listener
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Close any other open dropdowns
                    if (window.currentOpenDropdown && window.currentOpenDropdown !== menu) {
                        window.currentOpenDropdown.classList.add('hidden');
                    }

                    // Close any Alpine.js dropdowns
                    if (window.openAlpineDropdown) {
                        try {
                            window.openAlpineDropdown.__x.setData('open', false);
                        } catch (err) {
                            // Ignore errors if Alpine data is not available
                        }
                        window.openAlpineDropdown = null;
                    }

                    // Close sidebar on mobile
                    if (window.innerWidth < 1024) {
                        const sidebar = document.getElementById('sidebar');
                        const sidebarOverlay = document.getElementById('sidebarOverlay');
                        if (sidebar && !sidebar.classList.contains('-translate-x-full')) {
                            sidebar.classList.add('-translate-x-full');
                            sidebar.classList.remove('translate-x-0');
                            if (sidebarOverlay) {
                                sidebarOverlay.classList.add('opacity-0', 'pointer-events-none');
                                sidebarOverlay.classList.remove('opacity-100', 'pointer-events-auto');
                            }
                            if (window.Alpine && Alpine.store('sidebar')) {
                                Alpine.store('sidebar').open = false;
                            }
                        }
                    }

                    // Toggle menu
                    menu.classList.toggle('hidden');

                    // Update global tracking variables
                    if (menu.classList.contains('hidden')) {
                        window.currentOpenDropdown = null;
                    } else {
                        window.currentOpenDropdown = menu;

                        // Add click outside listener
                        setTimeout(() => {
                            const closeDropdown = (event) => {
                                if (!menu.contains(event.target) && !button.contains(event.target)) {
                                    menu.classList.add('hidden');
                                    window.currentOpenDropdown = null;
                                    document.removeEventListener('click', closeDropdown);
                                }
                            };
                            document.addEventListener('click', closeDropdown);
                        }, 10); // Increased timeout to ensure event binding happens after other events
                    }
                });
            }

            // Setup notification dropdown
            setupDropdown('mainNotificationButton', 'mainNotificationMenu');

            // Setup profile dropdown
            setupDropdown('mainProfileButton', 'mainProfileMenu');

            // Mobile sidebar toggle is now handled by Alpine.js
        });
    </script>

    <!-- Modern UI Enhancement Script -->
    <script src="{% static 'js/modern-ui.js' %}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
