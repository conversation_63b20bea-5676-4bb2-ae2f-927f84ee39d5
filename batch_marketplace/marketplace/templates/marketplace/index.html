{% extends 'marketplace/base.html' %}

{% block title %}Oleer Market - Modern Marketplace for Digital Services{% endblock %}

{% block description %}Secure marketplace for buying and selling digital services with cryptocurrency. USDT TRC-20 payments, instant delivery, and 24/7 support.{% endblock %}

{% block content %}
    <!-- Enhanced Hero Section -->
    <section class="marketplace-hero relative overflow-hidden py-20 lg:py-32">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0); background-size: 20px 20px;"></div>
        </div>

        <!-- Floating Elements -->
        <div class="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full animate-float"></div>
        <div class="absolute top-40 right-20 w-16 h-16 bg-white/5 rounded-full animate-float" style="animation-delay: 1s;"></div>
        <div class="absolute bottom-20 left-1/4 w-12 h-12 bg-white/10 rounded-full animate-float" style="animation-delay: 2s;"></div>

        <div class="container-xl relative z-10">
            <div class="flex flex-col lg:flex-row items-center gap-12">
                <div class="lg:w-1/2 text-center lg:text-left" data-animate>
                    <div class="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full text-sm font-medium text-white/90 mb-6">
                        <span class="w-2 h-2 bg-success-400 rounded-full mr-2 animate-pulse"></span>
                        Trusted by 10,000+ users worldwide
                    </div>

                    <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-white leading-tight">
                        Buy and Sell
                        <span class="bg-gradient-to-r from-blue-200 to-cyan-200 bg-clip-text text-transparent">
                            Digital Services
                        </span>
                        with Crypto
                    </h1>

                    <p class="text-xl lg:text-2xl mb-8 text-white/90 leading-relaxed max-w-2xl">
                        Secure marketplace connecting buyers and sellers with instant USDT TRC-20 payments,
                        automated delivery, and 24/7 support.
                    </p>

                    <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                        <a href="{% url 'vps_list' %}" class="btn btn-lg bg-white text-brand-600 hover:bg-white/90 shadow-xl hover:shadow-2xl interactive-lift">
                            <i class="fas fa-shopping-cart"></i>
                            <span>Browse Marketplace</span>
                            <i class="fas fa-arrow-right text-sm"></i>
                        </a>
                        {% if not user.is_authenticated %}
                            <a href="{% url 'register' %}" class="btn btn-lg btn-outline border-2 border-white text-white hover:bg-white hover:text-brand-600">
                                <i class="fas fa-user-plus"></i>
                                <span>Join Community</span>
                            </a>
                        {% endif %}
                    </div>

                    <!-- Trust Indicators -->
                    <div class="flex flex-wrap items-center gap-6 mt-8 justify-center lg:justify-start">
                        <div class="flex items-center text-white/80">
                            <i class="fas fa-shield-alt text-success-400 mr-2"></i>
                            <span class="text-sm font-medium">Secure Payments</span>
                        </div>
                        <div class="flex items-center text-white/80">
                            <i class="fas fa-clock text-info-400 mr-2"></i>
                            <span class="text-sm font-medium">Instant Delivery</span>
                        </div>
                        <div class="flex items-center text-white/80">
                            <i class="fas fa-headset text-warning-400 mr-2"></i>
                            <span class="text-sm font-medium">24/7 Support</span>
                        </div>
                    </div>
                </div>

                <div class="lg:w-1/2 relative" data-animate>
                    <!-- Main Image -->
                    <div class="relative z-10">
                        <img src="https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80"
                             alt="Modern Server Infrastructure"
                             class="rounded-2xl shadow-2xl w-full max-w-lg mx-auto interactive-lift">
                    </div>

                    <!-- Floating Cards -->
                    <div class="absolute -top-4 -left-4 bg-white rounded-xl p-4 shadow-xl animate-float hidden lg:block">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-success-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-check text-success-600"></i>
                            </div>
                            <div>
                                <div class="text-sm font-semibold text-text-primary">Payment Secured</div>
                                <div class="text-xs text-text-secondary">USDT TRC-20</div>
                            </div>
                        </div>
                    </div>

                    <div class="absolute -bottom-4 -right-4 bg-white rounded-xl p-4 shadow-xl animate-float hidden lg:block" style="animation-delay: 1s;">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-brand-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-bolt text-brand-600"></i>
                            </div>
                            <div>
                                <div class="text-sm font-semibold text-text-primary">Instant Delivery</div>
                                <div class="text-xs text-text-secondary">Automated</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Featured Products -->
    <section class="py-20 bg-surface-secondary">
        <div class="container-xl">
            <div class="text-center mb-16" data-animate>
                <div class="inline-flex items-center px-4 py-2 bg-brand-100 text-brand-700 rounded-full text-sm font-medium mb-4">
                    <i class="fas fa-star mr-2"></i>
                    Featured Products
                </div>
                <h2 class="text-3xl md:text-4xl font-bold text-text-primary mb-4">
                    Discover Premium Services
                </h2>
                <p class="text-lg text-text-secondary max-w-2xl mx-auto">
                    Hand-picked services from verified sellers with guaranteed quality and instant delivery
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {% if products %}
                    {% for product in products %}
                        <div class="marketplace-card group" data-animate>
                            <div class="relative overflow-hidden">
                                <img src="{{ product.image_url|default:'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80' }}"
                                     class="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
                                     alt="{{ product.title }}">

                                <!-- Overlay -->
                                <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                                <!-- Quick Actions -->
                                <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                    <button class="w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center text-text-secondary hover:text-danger-500 transition-colors"
                                            data-tooltip="Add to Wishlist">
                                        <i class="fas fa-heart"></i>
                                    </button>
                                </div>

                                <!-- Category Badge -->
                                <div class="absolute top-4 left-4">
                                    <span class="badge badge-sm bg-brand-500 text-white">
                                        {{ product.category|default:'VPS' }}
                                    </span>
                                </div>
                            </div>

                            <div class="card-body">
                                <div class="flex items-start justify-between mb-3">
                                    <h3 class="text-lg font-semibold text-text-primary group-hover:text-brand-600 transition-colors line-clamp-2">
                                        {{ product.title }}
                                    </h3>
                                    <div class="flex items-center ml-2">
                                        <i class="fas fa-star text-warning-400 text-sm"></i>
                                        <span class="text-sm text-text-secondary ml-1">4.8</span>
                                    </div>
                                </div>

                                <p class="text-text-secondary text-sm mb-4 line-clamp-3">
                                    {{ product.description|truncatewords:15 }}
                                </p>

                                <!-- Features -->
                                <div class="flex flex-wrap gap-2 mb-4">
                                    <span class="badge badge-sm badge-secondary">
                                        <i class="fas fa-bolt text-xs mr-1"></i>Instant
                                    </span>
                                    <span class="badge badge-sm badge-success">
                                        <i class="fas fa-shield-alt text-xs mr-1"></i>Secure
                                    </span>
                                    <span class="badge badge-sm badge-info">
                                        <i class="fas fa-headset text-xs mr-1"></i>Support
                                    </span>
                                </div>

                                <div class="flex items-center justify-between">
                                    <div class="flex flex-col">
                                        <span class="text-2xl font-bold text-brand-600">${{ product.price }}</span>
                                        <span class="text-xs text-text-tertiary">Starting from</span>
                                    </div>
                                    <a href="{% url 'vps_detail' product.id %}"
                                       class="btn btn-primary btn-sm interactive-lift">
                                        <span>View Details</span>
                                        <i class="fas fa-arrow-right text-xs"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="col-span-full text-center py-16" data-animate>
                        <div class="w-24 h-24 bg-surface-tertiary rounded-full flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-box-open text-3xl text-text-tertiary"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-text-primary mb-2">No products available yet</h3>
                        <p class="text-text-secondary mb-6">Be the first to list your services on our marketplace!</p>
                        {% if user.is_authenticated and user.profile.role == 'seller' %}
                            <a href="{% url 'add_account' %}" class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                <span>Add Your Service</span>
                            </a>
                        {% endif %}
                    </div>
                {% endif %}
            </div>

            <!-- View All Button -->
            {% if products %}
                <div class="text-center mt-12" data-animate>
                    <a href="{% url 'vps_list' %}" class="btn btn-outline btn-lg interactive-lift">
                        <span>View All Services</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            {% endif %}
        </div>
    </section>

    <!-- How It Works -->
    <section class="bg-gray-50 py-16">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">How Oleer Market Works</h2>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-white rounded-xl shadow-md p-8 text-center hover:shadow-lg transition-all duration-300">
                    <div class="mb-6">
                        <i class="fas fa-search text-5xl text-blue-500"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Find the Right Product</h3>
                    <p class="text-gray-600">Browse our marketplace to find solutions that match your requirements and budget.</p>
                </div>

                <div class="bg-white rounded-xl shadow-md p-8 text-center hover:shadow-lg transition-all duration-300">
                    <div class="mb-6">
                        <i class="fas fa-wallet text-5xl text-blue-500"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Pay with Crypto</h3>
                    <p class="text-gray-600">Secure payments using USDT TRC-20 cryptocurrency with automatic verification.</p>
                </div>

                <div class="bg-white rounded-xl shadow-md p-8 text-center hover:shadow-lg transition-all duration-300">
                    <div class="mb-6">
                        <i class="fas fa-server text-5xl text-blue-500"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Get Your Product</h3>
                    <p class="text-gray-600">After verification, the seller delivers your product with all necessary access credentials.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Trust Features -->
    <section class="py-16 px-4">
        <div class="container mx-auto">
            <h2 class="text-3xl font-bold text-center mb-12">Why Choose Oleer Market?</h2>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center">
                    <i class="fas fa-lock text-5xl text-blue-500 mb-4"></i>
                    <h3 class="text-xl font-semibold mb-3">Secure Payments</h3>
                    <p class="text-gray-600">All transactions use USDT TRC-20 with verification through TronScan.</p>
                </div>

                <div class="text-center">
                    <i class="fas fa-comment text-5xl text-blue-500 mb-4"></i>
                    <h3 class="text-xl font-semibold mb-3">Direct Communication</h3>
                    <p class="text-gray-600">Built-in chat system for direct communication with sellers.</p>
                </div>

                <div class="text-center">
                    <i class="fas fa-shield-alt text-5xl text-blue-500 mb-4"></i>
                    <h3 class="text-xl font-semibold mb-3">Dispute Resolution</h3>
                    <p class="text-gray-600">Admin support for resolving issues and handling refund requests.</p>
                </div>

                <div class="text-center">
                    <i class="fas fa-clipboard-list text-5xl text-blue-500 mb-4"></i>
                    <h3 class="text-xl font-semibold mb-3">Transparent History</h3>
                    <p class="text-gray-600">Complete order history and status tracking for all transactions.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Join Now CTA -->
    <section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-16">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold mb-4">Ready to Get Started?</h2>
            <p class="text-xl mb-8 max-w-3xl mx-auto">Join our community of buyers and sellers today.</p>

            {% if not user.is_authenticated %}
                <div class="flex flex-wrap justify-center gap-4">
                    <a href="{% url 'register' %}?role=buyer" class="inline-flex items-center px-6 py-3 bg-white text-blue-700 rounded-lg shadow-md hover:bg-gray-100 transition duration-300">
                        <i class="fas fa-user mr-2"></i>Join as Buyer
                    </a>
                    <a href="{% url 'register' %}?role=seller" class="inline-flex items-center px-6 py-3 border-2 border-white text-white rounded-lg hover:bg-white hover:text-blue-700 transition duration-300">
                        <i class="fas fa-store mr-2"></i>Join as Seller
                    </a>
                </div>
            {% else %}
                <a href="{% url 'vps_list' %}" class="inline-flex items-center px-6 py-3 bg-white text-blue-700 rounded-lg shadow-md hover:bg-gray-100 transition duration-300">
                    <i class="fas fa-shopping-cart mr-2"></i>Browse Products
                </a>
            {% endif %}
        </div>
    </section>
{% endblock %}
