@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');

/* CSS Variables for Design Tokens */
:root {
  /* Brand Colors */
  --color-brand-50: #f0f9ff;
  --color-brand-500: #0ea5e9;
  --color-brand-600: #0284c7;
  --color-brand-700: #0369a1;

  /* Semantic Colors */
  --color-success: #22c55e;
  --color-warning: #f59e0b;
  --color-danger: #ef4444;
  --color-info: #3b82f6;

  /* Surface Colors */
  --color-surface: #ffffff;
  --color-surface-secondary: #f8fafc;
  --color-surface-tertiary: #f1f5f9;

  /* Text Colors */
  --color-text-primary: #0f172a;
  --color-text-secondary: #475569;
  --color-text-tertiary: #64748b;

  /* Border Colors */
  --color-border: #e2e8f0;
  --color-border-secondary: #cbd5e1;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Border Radius */
  --radius-sm: 0.125rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;

  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark mode variables */
.dark {
  --color-surface: #1e293b;
  --color-surface-secondary: #334155;
  --color-surface-tertiary: #475569;
  --color-text-primary: #f8fafc;
  --color-text-secondary: #cbd5e1;
  --color-text-tertiary: #94a3b8;
  --color-border: #475569;
  --color-border-secondary: #64748b;
}

/* Enhanced Base Styles */
@layer base {
  * {
    @apply border-border;
  }

  html {
    @apply scroll-smooth;
  }

  body {
    @apply bg-surface text-text-primary font-sans antialiased;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    font-variation-settings: normal;
    text-rendering: optimizeLegibility;
  }

  /* Typography Scale */
  h1 {
    @apply text-3xl md:text-4xl font-bold text-text-primary leading-tight tracking-tight;
  }

  h2 {
    @apply text-2xl md:text-3xl font-bold text-text-primary leading-tight tracking-tight;
  }

  h3 {
    @apply text-xl md:text-2xl font-semibold text-text-primary leading-snug;
  }

  h4 {
    @apply text-lg md:text-xl font-semibold text-text-primary leading-snug;
  }

  h5 {
    @apply text-base md:text-lg font-medium text-text-primary;
  }

  h6 {
    @apply text-sm md:text-base font-medium text-text-primary;
  }

  p {
    @apply text-text-secondary leading-relaxed;
  }

  a {
    @apply text-brand-600 hover:text-brand-700 transition-colors duration-200;
    text-decoration: none;
  }

  a:hover {
    text-decoration: underline;
    text-decoration-thickness: 2px;
    text-underline-offset: 2px;
  }

  /* Focus styles */
  *:focus {
    @apply outline-none;
  }

  *:focus-visible {
    @apply ring-2 ring-brand-500 ring-offset-2 ring-offset-surface;
  }

  /* Selection styles */
  ::selection {
    @apply bg-brand-100 text-brand-900;
  }

  /* Scrollbar styles */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-surface-secondary;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-border-secondary rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-text-tertiary;
  }
}

/* Enhanced Component Library */
@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center justify-center gap-2 rounded-lg px-4 py-2.5 text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  .btn-xl {
    @apply px-8 py-4 text-lg;
  }

  .btn-primary {
    @apply bg-brand-500 text-white hover:bg-brand-600 active:bg-brand-700 focus:ring-brand-500 shadow-sm hover:shadow-md;
  }

  .btn-secondary {
    @apply bg-surface-secondary text-text-primary border border-border hover:bg-surface-tertiary focus:ring-brand-500;
  }

  .btn-success {
    @apply bg-success-500 text-white hover:bg-success-600 active:bg-success-700 focus:ring-success-500 shadow-sm hover:shadow-md;
  }

  .btn-warning {
    @apply bg-warning-500 text-white hover:bg-warning-600 active:bg-warning-700 focus:ring-warning-500 shadow-sm hover:shadow-md;
  }

  .btn-danger {
    @apply bg-danger-500 text-white hover:bg-danger-600 active:bg-danger-700 focus:ring-danger-500 shadow-sm hover:shadow-md;
  }

  .btn-ghost {
    @apply text-text-primary hover:bg-surface-secondary focus:ring-brand-500;
  }

  .btn-outline {
    @apply border border-border text-text-primary hover:bg-surface-secondary focus:ring-brand-500;
  }

  /* Card Components */
  .card {
    @apply bg-surface rounded-xl border border-border shadow-card overflow-hidden;
  }

  .card-hover {
    @apply transition-all duration-200 hover:shadow-card-hover hover:-translate-y-0.5;
  }

  .card-interactive {
    @apply cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1 active:translate-y-0;
  }

  .card-header {
    @apply px-6 py-4 border-b border-border bg-surface-secondary/50;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-border bg-surface-secondary/30;
  }

  /* Form Components */
  .form-group {
    @apply space-y-2;
  }

  .form-label {
    @apply block text-sm font-medium text-text-primary;
  }

  .form-input {
    @apply block w-full rounded-lg border border-border bg-surface px-3 py-2.5 text-sm placeholder:text-text-tertiary focus:border-brand-500 focus:ring-2 focus:ring-brand-500/20 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200;
  }

  .form-textarea {
    @apply form-input resize-none;
  }

  .form-select {
    @apply form-input pr-10 bg-no-repeat bg-right bg-[length:16px_16px] bg-[position:right_12px_center];
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  }

  .form-checkbox {
    @apply h-4 w-4 rounded border-border text-brand-600 focus:ring-brand-500 focus:ring-offset-0;
  }

  .form-radio {
    @apply h-4 w-4 border-border text-brand-600 focus:ring-brand-500 focus:ring-offset-0;
  }

  .form-error {
    @apply text-sm text-danger-600;
  }

  .form-help {
    @apply text-sm text-text-tertiary;
  }

  /* Input States */
  .input-error {
    @apply border-danger-300 focus:border-danger-500 focus:ring-danger-500/20;
  }

  .input-success {
    @apply border-success-300 focus:border-success-500 focus:ring-success-500/20;
  }

  /* Badge Components */
  .badge {
    @apply inline-flex items-center gap-1 rounded-full px-2.5 py-0.5 text-xs font-medium;
  }

  .badge-sm {
    @apply px-2 py-0.5 text-xs;
  }

  .badge-lg {
    @apply px-3 py-1 text-sm;
  }

  .badge-primary {
    @apply bg-brand-100 text-brand-800;
  }

  .badge-secondary {
    @apply bg-neutral-100 text-neutral-800;
  }

  .badge-success {
    @apply bg-success-100 text-success-800;
  }

  .badge-warning {
    @apply bg-warning-100 text-warning-800;
  }

  .badge-danger {
    @apply bg-danger-100 text-danger-800;
  }

  .badge-info {
    @apply bg-info-100 text-info-800;
  }

  /* Alert Components */
  .alert {
    @apply rounded-lg border p-4;
  }

  .alert-success {
    @apply border-success-200 bg-success-50 text-success-800;
  }

  .alert-warning {
    @apply border-warning-200 bg-warning-50 text-warning-800;
  }

  .alert-danger {
    @apply border-danger-200 bg-danger-50 text-danger-800;
  }

  .alert-info {
    @apply border-info-200 bg-info-50 text-info-800;
  }

  /* Navigation Components */
  .nav-link {
    @apply flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200;
  }

  .nav-link-active {
    @apply bg-brand-100 text-brand-700;
  }

  .nav-link-inactive {
    @apply text-text-secondary hover:bg-surface-secondary hover:text-text-primary;
  }

  /* Sidebar Components */
  .sidebar {
    @apply fixed inset-y-0 left-0 z-sidebar w-sidebar transform bg-primary-900 transition-transform duration-300 ease-in-out;
  }

  .sidebar-header {
    @apply flex h-16 items-center justify-between border-b border-primary-800 px-6;
  }

  .sidebar-nav {
    @apply flex-1 space-y-1 px-3 py-4;
  }

  .sidebar-link {
    @apply group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200;
  }

  .sidebar-link-active {
    @apply bg-brand-600 text-white;
  }

  .sidebar-link-inactive {
    @apply text-primary-300 hover:bg-primary-800 hover:text-white;
  }

  .sidebar-icon {
    @apply mr-3 h-5 w-5 flex-shrink-0;
  }

  /* Table Components */
  .table {
    @apply w-full border-collapse;
  }

  .table-header {
    @apply border-b border-border bg-surface-secondary;
  }

  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-text-secondary;
  }

  .table-row {
    @apply border-b border-border transition-colors duration-150 hover:bg-surface-secondary;
  }

  .table-cell {
    @apply px-6 py-4 text-sm;
  }

  /* Modal Components */
  .modal-overlay {
    @apply fixed inset-0 z-modal bg-black/50 transition-opacity;
  }

  .modal-container {
    @apply fixed inset-0 z-modal overflow-y-auto;
  }

  .modal-wrapper {
    @apply flex min-h-full items-center justify-center p-4;
  }

  .modal-content {
    @apply relative w-full max-w-lg transform overflow-hidden rounded-xl bg-surface shadow-xl transition-all;
  }

  .modal-header {
    @apply flex items-center justify-between border-b border-border px-6 py-4;
  }

  .modal-body {
    @apply px-6 py-4;
  }

  .modal-footer {
    @apply flex justify-end gap-3 border-t border-border px-6 py-4;
  }

  /* Dropdown Components */
  .dropdown {
    @apply relative inline-block;
  }

  .dropdown-menu {
    @apply absolute right-0 z-dropdown mt-2 w-56 origin-top-right rounded-lg bg-surface shadow-dropdown border border-border;
  }

  .dropdown-item {
    @apply block px-4 py-2 text-sm text-text-primary transition-colors duration-150 hover:bg-surface-secondary;
  }

  .dropdown-divider {
    @apply my-1 h-px bg-border;
  }

  /* Loading Components */
  .spinner {
    @apply inline-block h-4 w-4 animate-spin rounded-full border-2 border-solid border-current border-r-transparent;
  }

  .skeleton {
    @apply animate-pulse rounded bg-surface-secondary;
  }

  /* Status Components */
  .status-dot {
    @apply inline-block h-2 w-2 rounded-full;
  }

  .status-online {
    @apply bg-success-500;
  }

  .status-offline {
    @apply bg-neutral-400;
  }

  .status-busy {
    @apply bg-warning-500;
  }

  .status-away {
    @apply bg-danger-500;
  }

  /* Utility Components */
  .divider {
    @apply border-t border-border;
  }

  .divider-vertical {
    @apply border-l border-border;
  }

  .glass {
    @apply backdrop-blur-sm bg-white/80 border border-white/20;
  }

  .glass-dark {
    @apply backdrop-blur-sm bg-black/80 border border-white/10;
  }
}

/* Enhanced Utility Classes */
@layer utilities {
  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out;
  }

  .animate-fade-out {
    animation: fadeOut 0.3s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  .animate-scale-out {
    animation: scaleOut 0.2s ease-out;
  }

  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }

  .text-pretty {
    text-wrap: pretty;
  }

  /* Scrollbar utilities */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    @apply bg-surface-secondary;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    @apply bg-border-secondary rounded-full;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    @apply bg-text-tertiary;
  }

  /* Layout utilities */
  .container-fluid {
    @apply w-full max-w-none px-4 sm:px-6 lg:px-8;
  }

  .container-sm {
    @apply w-full max-w-screen-sm mx-auto px-4;
  }

  .container-md {
    @apply w-full max-w-screen-md mx-auto px-4;
  }

  .container-lg {
    @apply w-full max-w-screen-lg mx-auto px-4;
  }

  .container-xl {
    @apply w-full max-w-screen-xl mx-auto px-4;
  }

  .container-2xl {
    @apply w-full max-w-screen-2xl mx-auto px-4;
  }

  /* Interactive utilities */
  .interactive {
    @apply transition-all duration-200 hover:scale-105 active:scale-95;
  }

  .interactive-lift {
    @apply transition-all duration-200 hover:-translate-y-1 hover:shadow-lg;
  }

  .interactive-glow {
    @apply transition-all duration-200 hover:shadow-glow;
  }

  /* Focus utilities */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2;
  }

  .focus-ring-inset {
    @apply focus:outline-none focus:ring-2 focus:ring-inset focus:ring-brand-500;
  }

  /* State utilities */
  .disabled {
    @apply opacity-50 cursor-not-allowed pointer-events-none;
  }

  .loading {
    @apply opacity-75 cursor-wait pointer-events-none;
  }

  /* Gradient utilities */
  .gradient-brand {
    background: linear-gradient(135deg, theme('colors.brand.500'), theme('colors.brand.600'));
  }

  .gradient-success {
    background: linear-gradient(135deg, theme('colors.success.500'), theme('colors.success.600'));
  }

  .gradient-warning {
    background: linear-gradient(135deg, theme('colors.warning.500'), theme('colors.warning.600'));
  }

  .gradient-danger {
    background: linear-gradient(135deg, theme('colors.danger.500'), theme('colors.danger.600'));
  }
}

/* Component-specific styles */
.marketplace-hero {
  background: linear-gradient(135deg, theme('colors.brand.600'), theme('colors.brand.800'));
}

.marketplace-card {
  @apply card card-hover;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.marketplace-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.sidebar-modern {
  background: linear-gradient(180deg, theme('colors.primary.900'), theme('colors.primary.950'));
  backdrop-filter: blur(20px);
}

.navbar-glass {
  @apply glass border-b border-border/50;
  backdrop-filter: blur(20px);
}

/* Dark mode enhancements */
.dark .marketplace-card {
  @apply bg-surface border-border;
}

.dark .navbar-glass {
  @apply glass-dark;
}

/* Responsive design enhancements */
@media (max-width: 768px) {
  .mobile-optimized {
    @apply text-sm leading-relaxed;
  }

  .mobile-padding {
    @apply px-4 py-3;
  }

  .mobile-spacing {
    @apply space-y-4;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn {
    @apply border-2;
  }

  .card {
    @apply border-2;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Custom keyframes */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes scaleOut {
  from { transform: scale(1); opacity: 1; }
  to { transform: scale(0.9); opacity: 0; }
}
