// Enhanced Modern UI JavaScript for the marketplace

// Modern UI State Management
class UIState {
    constructor() {
        this.sidebar = {
            open: false,
            collapsed: false
        };
        this.theme = {
            dark: localStorage.getItem('darkMode') === 'true' || false
        };
        this.notifications = {
            unread: 0
        };
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.applyTheme();
        this.initializeAnimations();
    }
    
    setupEventListeners() {
        // Theme toggle
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-theme-toggle]')) {
                this.toggleTheme();
            }
        });
        
        // Sidebar management
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-sidebar-toggle]')) {
                this.toggleSidebar();
            }
        });
        
        // Close dropdowns when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.dropdown')) {
                this.closeAllDropdowns();
            }
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
    }
    
    toggleTheme() {
        this.theme.dark = !this.theme.dark;
        localStorage.setItem('darkMode', this.theme.dark);
        this.applyTheme();
    }
    
    applyTheme() {
        if (this.theme.dark) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
    }
    
    toggleSidebar() {
        this.sidebar.open = !this.sidebar.open;
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebarOverlay');
        
        if (sidebar) {
            if (this.sidebar.open) {
                sidebar.classList.remove('-translate-x-full');
                sidebar.classList.add('translate-x-0');
            } else {
                sidebar.classList.add('-translate-x-full');
                sidebar.classList.remove('translate-x-0');
            }
        }
        
        if (overlay) {
            if (this.sidebar.open) {
                overlay.classList.remove('opacity-0', 'pointer-events-none');
                overlay.classList.add('opacity-100');
            } else {
                overlay.classList.add('opacity-0', 'pointer-events-none');
                overlay.classList.remove('opacity-100');
            }
        }
    }
    
    closeAllDropdowns() {
        const dropdowns = document.querySelectorAll('.dropdown-menu');
        dropdowns.forEach(dropdown => {
            dropdown.classList.add('hidden');
        });
    }
    
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + K for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.querySelector('[data-search-input]');
            if (searchInput) {
                searchInput.focus();
            }
        }
        
        // Escape to close modals/dropdowns
        if (e.key === 'Escape') {
            this.closeAllDropdowns();
            this.closeModals();
        }
    }
    
    closeModals() {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.classList.add('hidden');
        });
    }
    
    initializeAnimations() {
        // Intersection Observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in');
                }
            });
        }, observerOptions);
        
        // Observe elements with animation classes
        document.querySelectorAll('[data-animate]').forEach(el => {
            observer.observe(el);
        });
    }
}

// Enhanced Notification System
class NotificationManager {
    constructor() {
        this.container = this.createContainer();
        this.notifications = [];
    }
    
    createContainer() {
        const container = document.createElement('div');
        container.className = 'fixed top-4 right-4 z-50 space-y-2';
        container.id = 'notification-container';
        document.body.appendChild(container);
        return container;
    }
    
    show(message, type = 'info', duration = 4000) {
        const notification = this.createNotification(message, type);
        this.container.appendChild(notification);
        this.notifications.push(notification);
        
        // Animate in
        requestAnimationFrame(() => {
            notification.classList.remove('translate-x-full', 'opacity-0');
            notification.classList.add('translate-x-0', 'opacity-100');
        });
        
        // Auto remove
        if (duration > 0) {
            setTimeout(() => {
                this.remove(notification);
            }, duration);
        }
        
        return notification;
    }
    
    createNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `
            transform translate-x-full opacity-0 transition-all duration-300 ease-out
            max-w-sm w-full bg-surface border border-border rounded-lg shadow-lg p-4
            flex items-center space-x-3
        `;
        
        const iconMap = {
            success: { icon: 'fas fa-check-circle', color: 'text-success-500' },
            error: { icon: 'fas fa-exclamation-circle', color: 'text-danger-500' },
            warning: { icon: 'fas fa-exclamation-triangle', color: 'text-warning-500' },
            info: { icon: 'fas fa-info-circle', color: 'text-info-500' }
        };
        
        const { icon, color } = iconMap[type] || iconMap.info;
        
        notification.innerHTML = `
            <div class="flex-shrink-0">
                <i class="${icon} ${color}"></i>
            </div>
            <div class="flex-1 text-sm text-text-primary">
                ${message}
            </div>
            <button class="flex-shrink-0 text-text-tertiary hover:text-text-primary transition-colors" onclick="notificationManager.remove(this.closest('.transform'))">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        return notification;
    }
    
    remove(notification) {
        notification.classList.remove('translate-x-0', 'opacity-100');
        notification.classList.add('translate-x-full', 'opacity-0');
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            const index = this.notifications.indexOf(notification);
            if (index > -1) {
                this.notifications.splice(index, 1);
            }
        }, 300);
    }
    
    success(message, duration) {
        return this.show(message, 'success', duration);
    }
    
    error(message, duration) {
        return this.show(message, 'error', duration);
    }
    
    warning(message, duration) {
        return this.show(message, 'warning', duration);
    }
    
    info(message, duration) {
        return this.show(message, 'info', duration);
    }
}

// Enhanced Form Utilities
class FormUtils {
    static validate(form) {
        const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
        let isValid = true;
        
        inputs.forEach(input => {
            const isInputValid = this.validateInput(input);
            if (!isInputValid) {
                isValid = false;
            }
        });
        
        return isValid;
    }
    
    static validateInput(input) {
        const value = input.value.trim();
        const type = input.type;
        let isValid = true;
        
        // Remove previous error states
        input.classList.remove('input-error');
        this.removeErrorMessage(input);
        
        // Required validation
        if (input.hasAttribute('required') && !value) {
            isValid = false;
            this.showError(input, 'This field is required');
        }
        
        // Type-specific validation
        if (value && type === 'email') {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                this.showError(input, 'Please enter a valid email address');
            }
        }
        
        if (value && type === 'password') {
            if (value.length < 8) {
                isValid = false;
                this.showError(input, 'Password must be at least 8 characters long');
            }
        }
        
        // Custom validation patterns
        const pattern = input.getAttribute('pattern');
        if (value && pattern) {
            const regex = new RegExp(pattern);
            if (!regex.test(value)) {
                isValid = false;
                const errorMessage = input.getAttribute('data-error-message') || 'Invalid format';
                this.showError(input, errorMessage);
            }
        }
        
        if (isValid) {
            input.classList.add('input-success');
        }
        
        return isValid;
    }
    
    static showError(input, message) {
        input.classList.add('input-error');
        
        const errorElement = document.createElement('div');
        errorElement.className = 'form-error mt-1';
        errorElement.textContent = message;
        errorElement.setAttribute('data-error-for', input.name || input.id);
        
        input.parentNode.appendChild(errorElement);
    }
    
    static removeErrorMessage(input) {
        const errorElement = input.parentNode.querySelector(`[data-error-for="${input.name || input.id}"]`);
        if (errorElement) {
            errorElement.remove();
        }
    }
}

// Enhanced API Utilities
class ApiUtils {
    static async request(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCsrfToken()
            }
        };
        
        const mergedOptions = { ...defaultOptions, ...options };
        
        try {
            const response = await fetch(url, mergedOptions);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            }
            
            return await response.text();
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }
    
    static getCsrfToken() {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrftoken') {
                return decodeURIComponent(value);
            }
        }
        return '';
    }
    
    static async markNotificationAsRead(notificationId) {
        try {
            const response = await this.request(`/notifications/${notificationId}/mark-read/`, {
                method: 'POST'
            });
            
            if (response.success) {
                // Update UI
                const countElement = document.querySelector('.notification-count');
                if (countElement) {
                    const currentCount = parseInt(countElement.textContent) || 0;
                    const newCount = Math.max(0, currentCount - 1);
                    countElement.textContent = newCount;
                    
                    if (newCount === 0) {
                        countElement.style.display = 'none';
                    }
                }
                
                const notificationElement = document.querySelector(`[data-notification-id="${notificationId}"]`);
                if (notificationElement) {
                    notificationElement.classList.remove('bg-info-50');
                    notificationElement.classList.add('bg-surface');
                }
            }
            
            return response;
        } catch (error) {
            notificationManager.error('Failed to mark notification as read');
            throw error;
        }
    }
}

// Enhanced Clipboard Utilities
class ClipboardUtils {
    static async copy(text) {
        try {
            await navigator.clipboard.writeText(text);
            notificationManager.success('Copied to clipboard!');
            return true;
        } catch (error) {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.opacity = '0';
            document.body.appendChild(textArea);
            textArea.select();
            
            try {
                document.execCommand('copy');
                notificationManager.success('Copied to clipboard!');
                return true;
            } catch (fallbackError) {
                notificationManager.error('Failed to copy to clipboard');
                return false;
            } finally {
                document.body.removeChild(textArea);
            }
        }
    }
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize global instances
    window.uiState = new UIState();
    window.notificationManager = new NotificationManager();
    
    // Auto-hide alerts
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => alert.remove(), 300);
        }, 5000);
    });
    
    // Initialize form validation
    const forms = document.querySelectorAll('form[data-validate]');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!FormUtils.validate(form)) {
                e.preventDefault();
            }
        });
        
        // Real-time validation
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('blur', () => FormUtils.validateInput(input));
        });
    });
    
    // Initialize copy buttons
    document.addEventListener('click', function(e) {
        if (e.target.matches('[data-copy]') || e.target.closest('[data-copy]')) {
            const button = e.target.matches('[data-copy]') ? e.target : e.target.closest('[data-copy]');
            const text = button.getAttribute('data-copy');
            ClipboardUtils.copy(text);
        }
    });
    
    // Initialize tooltips with better positioning
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
});

// Enhanced tooltip functions
function showTooltip(event) {
    const element = event.target;
    const text = element.getAttribute('data-tooltip');
    const position = element.getAttribute('data-tooltip-position') || 'top';
    
    const tooltip = document.createElement('div');
    tooltip.className = `
        absolute z-tooltip px-2 py-1 text-xs text-white bg-neutral-900 rounded shadow-lg
        pointer-events-none transition-opacity duration-200 opacity-0
    `;
    tooltip.textContent = text;
    tooltip.id = 'tooltip';
    
    document.body.appendChild(tooltip);
    
    const rect = element.getBoundingClientRect();
    const tooltipRect = tooltip.getBoundingClientRect();
    
    let left, top;
    
    switch (position) {
        case 'bottom':
            left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
            top = rect.bottom + 5;
            break;
        case 'left':
            left = rect.left - tooltipRect.width - 5;
            top = rect.top + (rect.height / 2) - (tooltipRect.height / 2);
            break;
        case 'right':
            left = rect.right + 5;
            top = rect.top + (rect.height / 2) - (tooltipRect.height / 2);
            break;
        default: // top
            left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
            top = rect.top - tooltipRect.height - 5;
    }
    
    tooltip.style.left = Math.max(5, Math.min(left, window.innerWidth - tooltipRect.width - 5)) + 'px';
    tooltip.style.top = Math.max(5, top) + 'px';
    
    requestAnimationFrame(() => {
        tooltip.classList.remove('opacity-0');
        tooltip.classList.add('opacity-100');
    });
}

function hideTooltip() {
    const tooltip = document.getElementById('tooltip');
    if (tooltip) {
        tooltip.classList.remove('opacity-100');
        tooltip.classList.add('opacity-0');
        setTimeout(() => tooltip.remove(), 200);
    }
}

// Legacy function support
function toggleSidebar() {
    if (window.uiState) {
        window.uiState.toggleSidebar();
    }
}

function markNotificationAsRead(notificationId) {
    ApiUtils.markNotificationAsRead(notificationId);
}

function copyToClipboard(text) {
    ClipboardUtils.copy(text);
}

function showToast(message, type = 'info') {
    if (window.notificationManager) {
        window.notificationManager.show(message, type);
    }
}

function validateForm(formId) {
    const form = document.getElementById(formId);
    return form ? FormUtils.validate(form) : false;
}
